<?php
/**
 * Vista para el reporte de balance general
 *
 * Variables disponibles:
 * @var array|null $reporte_data Datos del reporte generado
 * @var string $mes_seleccionado Mes seleccionado
 * @var string $anio_seleccionado Año seleccionado
 * @var int|null $id_centro_costo ID del centro de costo de la sesión
 * @var string $centro_costo_nombre Nombre del centro de costo de la sesión
 * @var string|null $success_text Mensaje de éxito a mostrar
 * @var string|null $success_display Estado de visualización de éxito ('show' o null)
 * @var string|null $error_text Mensaje de error a mostrar
 * @var string|null $error_display Estado de visualización de error ('show' o null)
 */
?>

<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Reporte de Balance General</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Include Bootstrap Datepicker CSS -->
	<link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
</head>

<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

        <!-- BEGIN #content -->
        <div id="content" class="app-content">
            <?php #region PAGE HEADER ?>
            <div class="d-flex align-items-center mb-3">
                <div>
                    <h4 class="mb-0">Reporte de Balance General</h4>
                    <p class="mb-0 text-muted">Balance diario de ingresos y egresos por mes</p>
                    <?php if ($centro_costo_nombre): ?>
                        <p class="mb-0 text-info"><i class="fa fa-building me-1"></i>Centro de Costo: <?php echo htmlspecialchars($centro_costo_nombre); ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <hr>
            <?php #endregion PAGE HEADER ?>

            <?php #region EXPLANATORY NOTE ?>
            <!-- Nota explicativa -->
            <div class="alert alert-info mb-3">
                <i class="fa fa-info-circle me-1"></i>
                <strong>Nota:</strong> Ingresos incluyen citas y ventas. Egresos incluyen órdenes de compra y gastos operativos.
                Solo se incluyen registros finalizados (con cierre asignado).
            </div>
            <?php #endregion EXPLANATORY NOTE ?>

            <?php #region FILTERS PANEL ?>
            <!-- Filters Panel -->
            <div class="panel panel-inverse">
                <div class="panel-heading">
                    <h4 class="panel-title">Filtros de Consulta</h4>
                </div>
                <div class="panel-body">
                    <form method="POST" id="reporte-form">
                        <input type="hidden" name="action" value="generar_reporte">

                        <div class="row g-3">
                            <!-- Mes -->
                            <div class="col-md-3">
                                <label for="mes_seleccionado" class="form-label">Mes <span class="text-danger">*</span></label>
                                <select class="form-select" id="mes_seleccionado" name="mes_seleccionado" required>
                                    <option value="">Seleccione un mes...</option>
                                    <option value="1" <?php echo ($mes_seleccionado == '1') ? 'selected' : ''; ?>>1 - Enero</option>
                                    <option value="2" <?php echo ($mes_seleccionado == '2') ? 'selected' : ''; ?>>2 - Febrero</option>
                                    <option value="3" <?php echo ($mes_seleccionado == '3') ? 'selected' : ''; ?>>3 - Marzo</option>
                                    <option value="4" <?php echo ($mes_seleccionado == '4') ? 'selected' : ''; ?>>4 - Abril</option>
                                    <option value="5" <?php echo ($mes_seleccionado == '5') ? 'selected' : ''; ?>>5 - Mayo</option>
                                    <option value="6" <?php echo ($mes_seleccionado == '6') ? 'selected' : ''; ?>>6 - Junio</option>
                                    <option value="7" <?php echo ($mes_seleccionado == '7') ? 'selected' : ''; ?>>7 - Julio</option>
                                    <option value="8" <?php echo ($mes_seleccionado == '8') ? 'selected' : ''; ?>>8 - Agosto</option>
                                    <option value="9" <?php echo ($mes_seleccionado == '9') ? 'selected' : ''; ?>>9 - Septiembre</option>
                                    <option value="10" <?php echo ($mes_seleccionado == '10') ? 'selected' : ''; ?>>10 - Octubre</option>
                                    <option value="11" <?php echo ($mes_seleccionado == '11') ? 'selected' : ''; ?>>11 - Noviembre</option>
                                    <option value="12" <?php echo ($mes_seleccionado == '12') ? 'selected' : ''; ?>>12 - Diciembre</option>
                                </select>
                            </div>

                            <!-- Año -->
                            <div class="col-md-3">
                                <label for="anio_seleccionado" class="form-label">Año <span class="text-danger">*</span></label>
                                <select class="form-select" id="anio_seleccionado" name="anio_seleccionado" required>
                                    <option value="">Seleccione un año...</option>
                                    <?php
                                    $anio_actual = date('Y');
                                    for ($anio = 2020; $anio <= 2030; $anio++):
                                    ?>
                                        <option value="<?php echo $anio; ?>" <?php echo ($anio_seleccionado == $anio) ? 'selected' : ''; ?>>
                                            <?php echo $anio; ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>

                            <!-- Botón Generar -->
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fa fa-chart-line fa-fw me-1"></i> Generar Reporte
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <?php #endregion FILTERS PANEL ?>

            <?php #region INFORMATIONAL SUBTITLE ?>
            <?php if ($reporte_data !== null && !empty($reporte_data['dias_balance'])): ?>
            <!-- Informational Subtitle -->
            <div class="mb-3">
                <h6 class="text-muted mb-0">
                    <i class="fa fa-calendar me-1"></i>
                    Consultando: <?php echo htmlspecialchars($reporte_data['mes_nombre'] ?? ''); ?> <?php echo htmlspecialchars($reporte_data['anio'] ?? ''); ?> -
                    <i class="fa fa-building me-1"></i><?php echo htmlspecialchars($centro_costo_nombre); ?>
                </h6>
            </div>
            <?php endif; ?>
            <?php #endregion INFORMATIONAL SUBTITLE ?>

            <?php #region RESULTS ?>
            <?php if ($reporte_data !== null && !empty($reporte_data['dias_balance'])): ?>
            <!-- Results Panel -->
            <div class="row">
                <div class="col-md-6">
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <div class="d-flex align-items-center w-100">
                                <div>
                                    <h4 class="panel-title mb-0">Balance General</h4>
                                </div>
                            </div>
                        </div>
                <div class="panel-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover table-sm mb-0" id="balance-table">
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 80px;" class="text-center">Acciones</th>
                                    <th style="width: 80px;">Día</th>
                                    <th class="text-end">Ingresos</th>
                                    <th class="text-end">Egresos</th>
                                    <th class="text-end">Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reporte_data['dias_balance'] as $dia_data): ?>
                                    <tr>
                                        <td class="text-center align-middle">
                                            <?php if ($dia_data['ingresos'] > 0 || $dia_data['egresos'] > 0): ?>
                                                <button type="button" class="btn btn-xs btn-primary"
                                                        onclick="verDetallesBalance('<?php echo htmlspecialchars($dia_data['fecha']); ?>')"
                                                        title="Ver Detalles">
                                                    <i class="fa fa-eye"></i>
                                                </button>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="align-middle"><strong><?php echo $dia_data['dia']; ?></strong></td>
                                        <td class="text-end align-middle"><?php echo htmlspecialchars($dia_data['ingresos_formateado']); ?></td>
                                        <td class="text-end align-middle"><?php echo htmlspecialchars($dia_data['egresos_formateado']); ?></td>
                                        <td class="text-end align-middle <?php echo $dia_data['balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                            <strong><?php echo htmlspecialchars($dia_data['balance_formateado']); ?></strong>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot class="table-dark">
                                <tr>
                                    <td></td>
                                    <td><strong>TOTALES</strong></td>
                                    <td class="text-end"><strong><?php echo htmlspecialchars($reporte_data['total_ingresos_formateado']); ?></strong></td>
                                    <td class="text-end"><strong><?php echo htmlspecialchars($reporte_data['total_egresos_formateado']); ?></strong></td>
                                    <td class="text-end <?php echo $reporte_data['balance_total'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <strong><?php echo htmlspecialchars($reporte_data['balance_total_formateado']); ?></strong>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
                </div>
                <!-- Gastos Operativos Agrupados Panel -->
                <div class="col-md-6">
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <div class="d-flex align-items-center w-100">
                                <div>
                                    <h4 class="panel-title mb-0">Gastos Operativos Agrupados</h4>
                                </div>
                            </div>
                        </div>
                        <div class="panel-body p-0">
                            <div id="gastos-agrupados-main-container">
                                <!-- Content will be populated when report is generated -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php elseif ($reporte_data !== null): ?>
            <!-- Empty State Panel -->
            <div class="panel panel-inverse">
                <div class="panel-body text-center py-5">
                    <i class="fa fa-chart-line fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No hay datos para mostrar</h5>
                    <p class="text-muted">No se encontraron registros finalizados para el mes y año seleccionados.</p>
                </div>
            </div>

            <?php else: ?>
            <!-- Initial State Panel -->
            <div class="panel panel-inverse">
                <div class="panel-body text-center py-5">
                    <i class="fa fa-chart-line fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Seleccione un mes y año para generar el reporte</h5>
                    <p class="text-muted">Complete los filtros de consulta y haga clic en "Generar Reporte" para ver el balance diario.</p>
                </div>
            </div>
            <?php endif; ?>
            <?php #endregion RESULTS ?>

        </div>
        <!-- END #content -->

        <?php #region BALANCE DETAILS MODAL ?>
        <!-- Balance Details Modal -->
        <div class="modal fade" id="balanceDetallesModal" tabindex="-1" aria-labelledby="balanceDetallesModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="balanceDetallesModalLabel">
                            <i class="fa fa-chart-line me-2"></i>Detalles del Balance - <span id="modal-fecha"></span>
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Loading State -->
                        <div id="balance-modal-loading" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                            <p class="mt-2 text-muted">Cargando detalles del balance...</p>
                        </div>

                        <!-- Error State -->
                        <div id="balance-detalles-error" class="alert alert-danger" style="display: none;">
                            <i class="fa fa-exclamation-triangle me-1"></i>
                            <span id="balance-error-message"></span>
                        </div>

                        <!-- Content -->
                        <div id="balance-modal-content" style="display: none;">
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs nav-tabs-v2" id="balanceDetailsTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link active" id="ingresos-tab" data-bs-toggle="tab" href="#ingresos" role="tab" aria-controls="ingresos" aria-selected="true">
                                        <i class="fa fa-arrow-down text-success me-1"></i>Ingresos (<span id="total-ingresos-tab"></span>)
                                    </a>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <a class="nav-link" id="egresos-tab" data-bs-toggle="tab" href="#egresos" role="tab" aria-controls="egresos" aria-selected="false">
                                        <i class="fa fa-arrow-up text-danger me-1"></i>Egresos (<span id="total-egresos-tab"></span>)
                                    </a>
                                </li>
                            </ul>

                            <!-- Tab content -->
                            <div class="tab-content mt-3" id="balanceDetailsTabContent">
                                <!-- Ingresos Tab -->
                                <div class="tab-pane fade show active" id="ingresos" role="tabpanel" aria-labelledby="ingresos-tab">
                                    <!-- Citas Section -->
                                    <div class="mb-4 p-3" style="background-color: rgba(0, 123, 255, 0.05); border-radius: 8px; border: 1px solid rgba(0, 123, 255, 0.1);">
                                        <h6 class="text-primary mb-3">
                                            <i class="fa fa-calendar-check me-1"></i>Citas
                                            (<span id="count-citas"></span> registros)
                                        </h6>
                                        <div id="citas-container">
                                            <!-- Citas table will be populated here -->
                                        </div>
                                    </div>

                                    <!-- Ventas Section -->
                                    <div class="mb-4 p-3" style="background-color: rgba(40, 167, 69, 0.05); border-radius: 8px; border: 1px solid rgba(40, 167, 69, 0.1);">
                                        <h6 class="text-success mb-3">
                                            <i class="fa fa-shopping-cart me-1"></i>Ventas
                                            (<span id="count-ventas"></span> registros)
                                        </h6>
                                        <div id="ventas-container">
                                            <!-- Ventas table will be populated here -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Egresos Tab -->
                                <div class="tab-pane fade" id="egresos" role="tabpanel" aria-labelledby="egresos-tab">
                                    <!-- Órdenes de Compra Section -->
                                    <div class="mb-4 p-3" style="background-color: rgba(220, 53, 69, 0.05); border-radius: 8px; border: 1px solid rgba(220, 53, 69, 0.1);">
                                        <h6 class="text-danger mb-3">
                                            <i class="fa fa-file-invoice me-1"></i>Órdenes de Compra
                                            (<span id="count-ordenes"></span> registros)
                                        </h6>
                                        <div id="ordenes-container">
                                            <!-- Órdenes table will be populated here -->
                                        </div>
                                    </div>

                                    <!-- Gastos Operativos Section -->
                                    <div class="mb-4 p-3" style="background-color: rgba(255, 193, 7, 0.05); border-radius: 8px; border: 1px solid rgba(255, 193, 7, 0.1);">
                                        <h6 class="text-warning mb-3">
                                            <i class="fa fa-receipt me-1"></i>Gastos Operativos
                                            (<span id="count-gastos"></span> registros)
                                        </h6>
                                        <div id="gastos-container">
                                            <!-- Gastos table will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fa fa-times me-1"></i>Cerrar
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php #endregion BALANCE DETAILS MODAL ?>

        <!-- BEGIN scroll-top-btn -->
        <a href="javascript:;" class="btn btn-icon btn-circle btn-theme btn-scroll-to-top" data-toggle="scroll-to-top">
            <i class="fa fa-angle-up"></i>
        </a>
        <!-- END scroll-top-btn -->
    </div>
    <!-- END #app -->

    <?php #region JS ?>
    <?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    document.getElementById('reporte-form').addEventListener('submit', function(e) {
        const mes = document.getElementById('mes_seleccionado').value;
        const anio = document.getElementById('anio_seleccionado').value;

        if (!mes || !anio) {
            e.preventDefault();
            showSweetAlertError('Error de Validación', 'Debe seleccionar un mes y año.');
            return false;
        }
    });

    // Handle Success/Error Messages from PHP
    <?php if (isset($success_display) && $success_display === 'show'): ?>
    showSweetAlertSuccess('¡Éxito!', '<?php echo addslashes($success_text ?? "Operación completada con éxito."); ?>');
    <?php endif; ?>

    <?php if (isset($error_display) && $error_display === 'show'): ?>
    showSweetAlertError('Error', '<?php echo addslashes($error_text ?? "Ha ocurrido un error."); ?>');
    <?php endif; ?>
});

// Global function to view balance details
window.verDetallesBalance = function(fecha) {
    const modal = new bootstrap.Modal(document.getElementById('balanceDetallesModal'));
    const modalLoading = document.getElementById('balance-modal-loading');
    const modalContent = document.getElementById('balance-modal-content');
    const modalError = document.getElementById('balance-detalles-error');
    const modalFecha = document.getElementById('modal-fecha');

    // Show modal and loading state
    modal.show();
    modalLoading.style.display = 'block';
    modalContent.style.display = 'none';
    modalError.style.display = 'none';
    modalFecha.textContent = fecha;

    // Fetch balance details
    const formData = new FormData();
    formData.append('action', 'get_balance_details');
    formData.append('fecha', fecha);

    fetch('reporte-balance-general', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        modalLoading.style.display = 'none';

        if (data.success) {
            // Populate modal content
            populateBalanceDetails(data.data);
            modalContent.style.display = 'block';
        } else {
            // Show error
            document.getElementById('balance-error-message').textContent = data.message || 'Error al cargar los detalles.';
            modalError.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        modalLoading.style.display = 'none';
        document.getElementById('balance-error-message').textContent = 'Error de conexión al cargar los detalles.';
        modalError.style.display = 'block';
    });
};

function populateBalanceDetails(data) {
    // Update tab totals
    document.getElementById('total-ingresos-tab').textContent = data.totales.total_ingresos_formateado;
    document.getElementById('total-egresos-tab').textContent = data.totales.total_egresos_formateado;

    // Update counts and totals
    document.getElementById('count-citas').textContent = data.citas.length;
    document.getElementById('total-citas').textContent = data.totales.ingresos_citas_formateado;
    document.getElementById('count-ventas').textContent = data.ventas.length;
    document.getElementById('total-ventas').textContent = data.totales.ingresos_ventas_formateado;
    document.getElementById('count-ordenes').textContent = data.ordenes.length;
    document.getElementById('total-ordenes').textContent = data.totales.egresos_ordenes_formateado;
    document.getElementById('count-gastos').textContent = data.gastos.length;

    // Populate tables
    populateCitasTable(data.citas);
    populateVentasTable(data.ventas);
    populateOrdenesTable(data.ordenes);
    populateGastosTable(data.gastos);
}

function populateCitasTable(citas) {
    const container = document.getElementById('citas-container');

    if (citas.length === 0) {
        container.innerHTML = '<div class="alert alert-info"><i class="fa fa-info-circle me-1"></i>No hay citas registradas para esta fecha.</div>';
        return;
    }

    let totalCitas = 0;
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-sm table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Empleado</th>
                        <th>Hora Inicio</th>
                        <th>Hora Fin</th>
                        <th>Método Pago</th>
                        <th class="text-end">Valor</th>
                    </tr>
                </thead>
                <tbody>`;

    citas.forEach(cita => {
        const horaInicio = new Date(cita.fecha_inicio).toLocaleTimeString('es-CO', {hour: '2-digit', minute: '2-digit'});
        const horaFin = new Date(cita.fecha_fin).toLocaleTimeString('es-CO', {hour: '2-digit', minute: '2-digit'});
        totalCitas += parseFloat(cita.valor_total) || 0;

        tableHtml += `
            <tr>
                <td>${cita.id}</td>
                <td>${cita.nombre_empleado}</td>
                <td>${horaInicio}</td>
                <td>${horaFin}</td>
                <td>${cita.metodo_pago}</td>
                <td class="text-end">${cita.valor_total_formateado}</td>
            </tr>`;
    });

    const totalFormateado = '$' + new Intl.NumberFormat('es-CO').format(totalCitas);
    tableHtml += `
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <th class="fw-bold">Total:</th>
                        <th colspan="4"></th>
                        <th class="text-end fw-bold">${totalFormateado}</th>
                    </tr>
                </tfoot>
            </table>
        </div>`;

    container.innerHTML = tableHtml;
}

function populateVentasTable(ventas) {
    const container = document.getElementById('ventas-container');

    if (ventas.length === 0) {
        container.innerHTML = '<div class="alert alert-info"><i class="fa fa-info-circle me-1"></i>No hay ventas registradas para esta fecha.</div>';
        return;
    }

    let totalVentas = 0;
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-sm table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Cliente</th>
                        <th>Celular</th>
                        <th>Hora</th>
                        <th>Método Pago</th>
                        <th class="text-end">Valor</th>
                    </tr>
                </thead>
                <tbody>`;

    ventas.forEach(venta => {
        const hora = new Date(venta.fecha).toLocaleTimeString('es-CO', {hour: '2-digit', minute: '2-digit'});
        totalVentas += parseFloat(venta.valor_total) || 0;

        tableHtml += `
            <tr>
                <td>${venta.id}</td>
                <td>${venta.nombre_cliente}</td>
                <td>${venta.celular_cliente || '-'}</td>
                <td>${hora}</td>
                <td>${venta.metodo_pago}</td>
                <td class="text-end">${venta.valor_total_formateado}</td>
            </tr>`;
    });

    const totalFormateado = '$' + new Intl.NumberFormat('es-CO').format(totalVentas);
    tableHtml += `
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <th class="fw-bold">Total:</th>
                        <th colspan="4"></th>
                        <th class="text-end fw-bold">${totalFormateado}</th>
                    </tr>
                </tfoot>
            </table>
        </div>`;

    container.innerHTML = tableHtml;
}

function populateOrdenesTable(ordenes) {
    const container = document.getElementById('ordenes-container');

    if (ordenes.length === 0) {
        container.innerHTML = '<div class="alert alert-info"><i class="fa fa-info-circle me-1"></i>No hay órdenes de compra registradas para esta fecha.</div>';
        return;
    }

    let totalOrdenes = 0;
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-sm table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th># Referencia</th>
                        <th>Proveedor</th>
                        <th>Usuario</th>
                        <th>Hora</th>
                        <th class="text-end">Valor</th>
                    </tr>
                </thead>
                <tbody>`;

    ordenes.forEach(orden => {
        const hora = new Date(orden.fecha).toLocaleTimeString('es-CO', {hour: '2-digit', minute: '2-digit'});
        totalOrdenes += parseFloat(orden.valor_total) || 0;

        tableHtml += `
            <tr>
                <td>${orden.id}</td>
                <td>${orden.numero_referencia || '-'}</td>
                <td>${orden.nombre_proveedor}</td>
                <td>${orden.nombre_usuario}</td>
                <td>${hora}</td>
                <td class="text-end">${orden.valor_total_formateado}</td>
            </tr>`;
    });

    const totalFormateado = '$' + new Intl.NumberFormat('es-CO').format(totalOrdenes);
    tableHtml += `
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <th class="fw-bold">Total:</th>
                        <th colspan="4"></th>
                        <th class="text-end fw-bold">${totalFormateado}</th>
                    </tr>
                </tfoot>
            </table>
        </div>`;

    container.innerHTML = tableHtml;
}

function populateGastosTable(gastos) {
    const container = document.getElementById('gastos-container');

    if (gastos.length === 0) {
        container.innerHTML = '<div class="alert alert-info"><i class="fa fa-info-circle me-1"></i>No hay gastos operativos registrados para esta fecha.</div>';
        return;
    }

    let totalGastos = 0;
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-sm table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Descripción</th>
                        <th>Hora</th>
                        <th class="text-end">Valor</th>
                    </tr>
                </thead>
                <tbody>`;

    gastos.forEach(gasto => {
        const hora = new Date(gasto.fecha).toLocaleTimeString('es-CO', {hour: '2-digit', minute: '2-digit'});
        totalGastos += parseFloat(gasto.valor) || 0;

        tableHtml += `
            <tr>
                <td>${gasto.id}</td>
                <td>${gasto.descripcion}</td>
                <td>${hora}</td>
                <td class="text-end">${gasto.valor_formateado}</td>
            </tr>`;
    });

    const totalFormateado = '$' + new Intl.NumberFormat('es-CO').format(totalGastos);
    tableHtml += `
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <th class="fw-bold">Total:</th>
                        <th colspan="2"></th>
                        <th class="text-end fw-bold">${totalFormateado}</th>
                    </tr>
                </tfoot>
            </table>
        </div>`;

    container.innerHTML = tableHtml;
}

// Global function to populate main gastos agrupados panel
window.populateGastosAgrupadosMain = function(gastosAgrupados) {
    const container = document.getElementById('gastos-agrupados-main-container');

    if (!gastosAgrupados || gastosAgrupados.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fa fa-receipt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No hay gastos operativos</h5>
                <p class="text-muted">No se encontraron gastos operativos finalizados para el período seleccionado.</p>
            </div>`;
        return;
    }

    let totalGastosAgrupados = 0;
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-hover table-sm mb-0">
                <thead class="table-dark">
                    <tr>
                        <th>Descripción</th>
                        <th class="text-end">Total</th>
                    </tr>
                </thead>
                <tbody>`;

    gastosAgrupados.forEach(grupo => {
        totalGastosAgrupados += parseFloat(grupo.total) || 0;

        tableHtml += `
            <tr>
                <td class="align-middle">${grupo.descripcion}</td>
                <td class="text-end align-middle">${grupo.total_formateado}</td>
            </tr>`;
    });

    const totalFormateado = '$' + new Intl.NumberFormat('es-CO').format(totalGastosAgrupados);
    tableHtml += `
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <td class="fw-bold">TOTAL</td>
                        <td class="text-end fw-bold">${totalFormateado}</td>
                    </tr>
                </tfoot>
            </table>
        </div>`;

    container.innerHTML = tableHtml;
};
</script>

</body>

</html>
