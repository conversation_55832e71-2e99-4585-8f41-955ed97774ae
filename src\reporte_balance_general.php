<?php

declare(strict_types=1);

use App\classes\Cita;
use App\classes\Venta;
use App\classes\OrdenCompra;
use App\classes\GastoOperativo;
use App\classes\CentroCosto;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en reporte_balance_general.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Variables para manejo de errores y mensajes
$error_display   = null;
$error_text      = null;
$success_display = null;
$success_text    = null;

// Variables para los datos del reporte
$reporte_data        = null;
$mes_seleccionado    = '';
$anio_seleccionado   = '';
$id_centro_costo     = null;
$centro_costo_nombre = '';

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    #region Generate Report
    if ($action === 'generar_reporte') {
        $mes_seleccionado  = trim($_POST['mes_seleccionado'] ?? '');
        $anio_seleccionado = trim($_POST['anio_seleccionado'] ?? '');

        // Validaciones
        if (empty($mes_seleccionado) || empty($anio_seleccionado)) {
            $error_display = 'show';
            $error_text    = 'Debe seleccionar un mes y año.';
        } elseif (!is_numeric($mes_seleccionado) || $mes_seleccionado < 1 || $mes_seleccionado > 12) {
            $error_display = 'show';
            $error_text    = 'El mes seleccionado no es válido.';
        } elseif (!is_numeric($anio_seleccionado) || $anio_seleccionado < 2020 || $anio_seleccionado > 2030) {
            $error_display = 'show';
            $error_text    = 'El año seleccionado no es válido.';
        } else {
            try {
                // Establecer zona horaria
                date_default_timezone_set('America/Bogota');

                // Obtener centro de costo de la sesión
                $id_centro_costo = $_SESSION[CENTRO_COSTO_SESSION] ?? null;
                if (!$id_centro_costo) {
                    $error_display = 'show';
                    $error_text    = 'No hay un centro de costo seleccionado en la sesión.';
                } else {
                    // Obtener nombre del centro de costo
                    $centro_costo = CentroCosto::get($id_centro_costo, $conexion);
                    if ($centro_costo) {
                        $centro_costo_nombre = $centro_costo->getNombre();
                    }

                    // Generar datos del reporte
                    $reporte_data = generarReporteBalanceGeneral($conexion, (int)$mes_seleccionado, (int)$anio_seleccionado, $id_centro_costo);

                    if (empty($reporte_data['dias_balance'])) {
                        $error_display = 'show';
                        $error_text    = 'No se encontraron datos para el mes y año seleccionados.';
                    }
                }

            } catch (Exception $e) {
                $error_display = 'show';
                $error_text    = 'Error al generar el reporte: ' . $e->getMessage();
            }
        }
    }
    #endregion Generate Report

    #region Get Balance Details
    if ($action === 'get_balance_details') {
        header('Content-Type: application/json');

        try {
            $fecha = trim($_POST['fecha'] ?? '');

            // Validaciones
            if (empty($fecha)) {
                echo json_encode(['success' => false, 'message' => 'Fecha requerida.']);
                exit;
            }

            // Obtener centro de costo de la sesión
            $id_centro_costo = $_SESSION[CENTRO_COSTO_SESSION] ?? null;
            if (!$id_centro_costo) {
                echo json_encode(['success' => false, 'message' => 'No hay un centro de costo seleccionado en la sesión.']);
                exit;
            }

            // Obtener registros detallados
            $citas = Cita::obtenerRegistrosPorFecha($fecha, $id_centro_costo, $conexion);
            $ventas = Venta::obtenerRegistrosPorFecha($fecha, $id_centro_costo, $conexion);
            $ordenes = OrdenCompra::obtenerRegistrosPorFecha($fecha, $id_centro_costo, $conexion);
            $gastos = GastoOperativo::obtenerRegistrosPorFecha($fecha, $id_centro_costo, $conexion);

            // Calcular totales
            $total_ingresos_citas = array_sum(array_column($citas, 'valor_total'));
            $total_ingresos_ventas = array_sum(array_column($ventas, 'valor_total'));
            $total_egresos_ordenes = array_sum(array_column($ordenes, 'valor_total'));
            $total_egresos_gastos = array_sum(array_column($gastos, 'valor'));

            $total_ingresos = $total_ingresos_citas + $total_ingresos_ventas;
            $total_egresos = $total_egresos_ordenes + $total_egresos_gastos;

            echo json_encode([
                'success' => true,
                'data' => [
                    'fecha' => $fecha,
                    'citas' => $citas,
                    'ventas' => $ventas,
                    'ordenes' => $ordenes,
                    'gastos' => $gastos,
                    'totales' => [
                        'ingresos_citas' => $total_ingresos_citas,
                        'ingresos_ventas' => $total_ingresos_ventas,
                        'total_ingresos' => $total_ingresos,
                        'egresos_ordenes' => $total_egresos_ordenes,
                        'egresos_gastos' => $total_egresos_gastos,
                        'total_egresos' => $total_egresos,
                        'ingresos_citas_formateado' => '$' . number_format($total_ingresos_citas, 0, ',', '.'),
                        'ingresos_ventas_formateado' => '$' . number_format($total_ingresos_ventas, 0, ',', '.'),
                        'total_ingresos_formateado' => '$' . number_format($total_ingresos, 0, ',', '.'),
                        'egresos_ordenes_formateado' => '$' . number_format($total_egresos_ordenes, 0, ',', '.'),
                        'egresos_gastos_formateado' => '$' . number_format($total_egresos_gastos, 0, ',', '.'),
                        'total_egresos_formateado' => '$' . number_format($total_egresos, 0, ',', '.')
                    ]
                ]
            ]);
            exit;

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error al obtener los detalles: ' . $e->getMessage()]);
            exit;
        }
    }
    #endregion Get Balance Details
}
#endregion Handle POST Actions

#region try
try {
    // Establecer zona horaria
    date_default_timezone_set('America/Bogota');

    // Set default month and year to current date if not coming from POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $mes_seleccionado = date('n'); // Current month (1-12)
        $anio_seleccionado = date('Y'); // Current year
    }

    // Obtener centro de costo de la sesión
    $id_centro_costo = $_SESSION[CENTRO_COSTO_SESSION] ?? null;
    if ($id_centro_costo) {
        $centro_costo = CentroCosto::get($id_centro_costo, $conexion);
        if ($centro_costo) {
            $centro_costo_nombre = $centro_costo->getNombre();
        }
    }

} catch (PDOException $e) {
    $error_display = 'show';
    $error_text    = "Error de base de datos al cargar los datos iniciales.";
} catch (Exception $e) {
    $error_display = 'show';
    $error_text    = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion try

/**
 * Función para generar los datos del reporte de balance general
 */
function generarReporteBalanceGeneral(PDO $conexion, int $mes, int $anio, int $id_centro_costo): array
{
    try {
        // Calcular el número de días en el mes
        $dias_en_mes = cal_days_in_month(CAL_GREGORIAN, $mes, $anio);
        
        $dias_balance = [];
        $total_ingresos = 0;
        $total_egresos = 0;
        
        // Iterar por cada día del mes
        for ($dia = 1; $dia <= $dias_en_mes; $dia++) {
            $fecha = sprintf('%04d-%02d-%02d', $anio, $mes, $dia);
            
            // Calcular ingresos del día (Citas + Ventas)
            $ingresos_citas     = Cita::calcularIngresosPorFecha($fecha, $id_centro_costo, $conexion);
            $ingresos_ventas    = Venta::calcularIngresosPorFecha($fecha, $id_centro_costo, $conexion);
            $total_ingresos_dia = $ingresos_citas + $ingresos_ventas;

            // Calcular egresos del día (Órdenes de Compra + Gastos Operativos)
            $egresos_ordenes   = OrdenCompra::calcularEgresosPorFecha($fecha, $id_centro_costo, $conexion);
            $egresos_gastos    = GastoOperativo::calcularEgresosPorFecha($fecha, $id_centro_costo, $conexion);
            $total_egresos_dia = $egresos_ordenes + $egresos_gastos;
            
            // Calcular balance del día
            $balance_dia = $total_ingresos_dia - $total_egresos_dia;
            
            $dias_balance[] = [
                'dia'                 => $dia,
                'fecha'               => $fecha,
                'ingresos'            => $total_ingresos_dia,
                'egresos'             => $total_egresos_dia,
                'balance'             => $balance_dia,
                'ingresos_formateado' => '$' . number_format($total_ingresos_dia, 0, ',', '.'),
                'egresos_formateado'  => '$' . number_format($total_egresos_dia, 0, ',', '.'),
                'balance_formateado'  => '$' . number_format($balance_dia, 0, ',', '.')
            ];
            
            $total_ingresos += $total_ingresos_dia;
            $total_egresos += $total_egresos_dia;
        }
        
        $balance_total = $total_ingresos - $total_egresos;
        
        return [
            'dias_balance'              => $dias_balance,
            'total_ingresos'            => $total_ingresos,
            'total_egresos'             => $total_egresos,
            'balance_total'             => $balance_total,
            'total_ingresos_formateado' => '$' . number_format($total_ingresos, 0, ',', '.'),
            'total_egresos_formateado'  => '$' . number_format($total_egresos, 0, ',', '.'),
            'balance_total_formateado'  => '$' . number_format($balance_total, 0, ',', '.'),
            'mes_nombre'                => obtenerNombreMes($mes),
            'anio'                      => $anio
        ];
        
    } catch (Exception $e) {
        throw new Exception("Error al generar el reporte de balance general: " . $e->getMessage());
    }
}

/**
 * Función auxiliar para obtener el nombre del mes
 */
function obtenerNombreMes(int $mes): string
{
    $meses = [
        1 => 'Enero',      2  => 'Febrero', 3  => 'Marzo',     4  => 'Abril',
        5 => 'Mayo',       6  => 'Junio',   7  => 'Julio',     8  => 'Agosto',
        9 => 'Septiembre', 10 => 'Octubre', 11 => 'Noviembre', 12 => 'Diciembre'
    ];

    return $meses[$mes] ?? 'Mes Desconocido';
}

// Include the view
require_once __ROOT__ . '/views/reporte_balance_general.view.php';

?>
